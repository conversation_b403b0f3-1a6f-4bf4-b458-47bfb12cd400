import { registerTheme } from '@appmaker-xyz/core';
import { appmaker } from '@appmaker-xyz/core';
import { blocks } from './blocks';
import { pages } from './Pages/index';
import { addFilter } from '@appmaker-xyz/core';
import { registerActions } from './actions';
import { appmakerFunctions } from '@appmaker-xyz/core';
import { addProductCollection } from './functions';
import registerToolBarIcons from '../helpers/registerToolBarIcons';
import BottomTabBar from './bottomTabs/BottomTabBar';
import { runDataSource } from '@appmaker-xyz/core';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { setSettings } from '../config';
import { Platform } from 'react-native';
import SplashScreen from './Components/Splash/SplashScreen';
import { updateFirebaseEvents } from './utils/UpdateFirebaseEvents';
import { extractQueryParamsFromURL } from './utils/Helper';

const backSvg = `<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M10 5L3 12L10 19" stroke="#414040" stroke-linecap="round"/>
<path d="M4 12H22" stroke="#414040" stroke-linecap="round"/>
</svg>`;

const dataSource = {
  attributes: {},
  source: "shopify",
};

appmaker.addFilter(
  "appmaker-splash-screen-timeout",
  "jockey-mobile",
  () => 4000 // timeout in ms
);

// to change the component
appmaker.addFilter(
  "appmaker-splash-screen-component",
  "jockey-mobile",
  () => SplashScreen
);


export function activate(params) {
  setSettings(params?.settings);

  addFilter(
    'appmaker-navigation-screen-options',
    'jockey-mobile',
    (screenOptions) => {

      // For transition to be same in iOS and Android horizontal.
      const transitionProps = {
        gestureEnabled: false,
        gestureDirection: 'horizontal',
        cardStyleInterpolator: ({ current, layouts }) => ({
          cardStyle: {
            transform: [
              {
                translateX: current.progress.interpolate({
                  inputRange: [0, 1],
                  outputRange: [layouts.screen.width, 0],
                }),
              },
            ],
          },
        }),
      };

      return {
        ...screenOptions,
        ...transitionProps,
      };
    },
  );
  addFilter(
    'custom-drawer-style',
    'custom-drawer-width-style',
    (oldStyle) => {
      return {
        ...oldStyle,
        width: '100%',
      };
    },
  );

  addFilter('app-back-button-icon', 'back-icon', () => {
    return {
      svgXml: backSvg,
    };
  });

  appmaker.addFilter(
    'shopify-filter-components',
    'core-theme',
    (filterOptionsComponents) => {
      filterOptionsComponents.DROPDOWN = FilterTabsIndex;
      return filterOptionsComponents;
    },
  );

  appmaker.addFilter(
    'deeplink-url-custom-action',
    'deeplink-custom-action',
    (currentAction, { url }) => {
      try {
        extractQueryParamsFromURL(url);
      } catch (error) {
        console.log("getAllQueryParams error", error);
      }
      return currentAction
    },
  );
  // appmaker.addFilter('home-tab-page-id', 'jockey-mobile', () => 'B2zEs4fivd');

  addFilter("home-custom-bottom-tab", "jockey-mobile", () => {
    return BottomTabBar;
  });

  appmaker.addFilter(
    'inapp-page-data-response',
    'jockey-mobile',
    (data, { pageId }) => {
      var newData = { ...data };
      var newAttributes = data.attributes;
      if (pageId === 'DrawerMenu' || pageId === 'WishList' || pageId === 'LoginOptions' || pageId === 'searchPage') {

        newData.attributes = { ...newAttributes, headerShown: false };

      }
      else if (pageId === 'home') {
        newData = {
          ...data,
          stickyHeader: {
            blocks: [
              {
                clientId: 'sticky-search',
                name: 'custom/sticky-header-for-search',
              },
              ...(Platform.OS === 'android'
                ? [{
                  clientId: 'teasdfssttes',
                  name: 'custom/sticky-header-android',
                }]
                : []),
            ],
          },
          stickyFooter: {
            blocks: [
              ...(Platform.OS === 'ios'
                ? [{
                  clientId: 'teasdfsst',
                  name: 'custom/sticky-header',
                }]
                : []),
            ],
          }
        };
      }
      else if (pageId === 'groove') {
        newData = {
          ...data,
          stickyHeader: {
            blocks: [
              {
                clientId: 'sticky-search',
                name: 'custom/sticky-header-for-search',
              },
              ...(Platform.OS === 'android'
                ? [{
                  clientId: 'teasdfssttes',
                  name: 'custom/sticky-header-android',
                }]
                : []),
            ],
          },
          stickyFooter: {
            blocks: [
              ...(Platform.OS === 'ios'
                ? [{
                  clientId: 'teasdfsst',
                  name: 'custom/sticky-header',
                }]
                : []),
            ],
          }
        };
      }
      else if (pageId == "myaccountpage") {
        newData.attributes = {
          ...newAttributes,

          insideSafeAreaView: true,
          enableSafeAreaTopInset: true,
        };
      }

      return newData;
    }


  )

  appmaker.addFilter(
    'shopify-custom-collection-response',
    'shopify-custom-collection-response',
    async (collectionList, params, dependencies) => {
      const collectionData = await collectionList;
      const metafields = collectionData?.data?.data?.collection?.metafields
      const filters = collectionData?.data?.data?.collection?.products?.filters;
      const appliedFilter = dependencies?.filterParams?.filter;
      const collectionId = collectionData?.data?.data?.collection?.id;

      const appliedColorFilter =
        appliedFilter && appliedFilter['filter.p.m.custom.filter_colour']
          ? Object?.values(appliedFilter['filter.p.m.custom.filter_colour'])?.map(
            (item) => item.label,
          )
          : [];

      if (
        appliedColorFilter &&
        appliedColorFilter.length > 0) {
        let products = collectionData?.data?.data?.collection?.products?.edges;

        // Filter products based on the applied color filter
        products = products.filter((productNode) => {

          const filteredProduct = productNode?.node?.color_variant?.references?.nodes?.find((product) => {
            return appliedColorFilter?.some(
              (filterValue) =>
                product?.colour_family_metafield?.value === filterValue
            );
          });

          // If filteredProduct is null or undefined, exclude this product from the list
          return filteredProduct;
        });


        // Ensure the collectionData path exists before assignment
        if (collectionData?.data?.data?.collection?.products) {
          collectionData.data.data.collection.products.edges = products;
        }
      }



      try {
        if (params?.surface === 'collection-page') {
          let productListLength = collectionData?.data?.data?.collection?.products?.edges?.length || 0;

          if (productListLength > 0) {
            const currentPage = dependencies?.page || 1;
            const productsPerPage = 20;
            let currentProductNumber = (currentPage - 1) * productsPerPage;  // Start from the correct page
            const metaObjectResFields = metafields.find(item => item?.key === "template_name")?.reference.fields
            const gender = metaObjectResFields.find(item => item?.key === "gender_name")?.value
            const isWomen = gender == "Female";

            const sizeFilterProductCount = isWomen ? 12 : 4;
            const priceFilterProductCount = isWomen ? 24 : 16;
            const buzzInTownCount = isWomen ? 4 : 8;

            const customSizeFilterBlock = {
              ___appmakercustomblock: {
                blockSpan: 'full',
                block: {
                  name: 'custom/plp-smart-size-filter',
                  clientId: 'product-lists',
                  isValid: true,
                  attributes: {
                    __experimentalDisableListItemParser: true,
                    filters,
                    appliedFilter,
                  },
                },
              },
            };

            const customPriceFilterBlock = {
              ___appmakercustomblock: {
                blockSpan: 'full',
                block: {
                  name: 'custom/plp-smart-price-filter',
                  clientId: 'product-lists',
                  isValid: true,
                  attributes: {
                    __experimentalDisableListItemParser: true,
                    fields: metaObjectResFields,
                  },
                },
              },
            };

            // const customBuzzInTownBlock = {
            //   ___appmakercustomblock: {
            //     blockSpan: 'full',
            //     block: {
            //       name: 'custom/plp-buzz-in-town',
            //       clientId: 'product-lists',
            //       isValid: true,
            //       attributes: {
            //         __experimentalDisableListItemParser: true,
            //         collectionId,
            //         productIds: metafields.find(item => item?.key === "buzz_in_town_products")?.value
            //       },
            //     },
            //   },
            // };
            if (productListLength > 0 && (currentProductNumber < Math.max(sizeFilterProductCount, priceFilterProductCount))) {
              for (let index = 0; index < productListLength; index++, currentProductNumber++) {
                if (currentProductNumber == sizeFilterProductCount) {
                  collectionData.data.data.collection.products.edges.splice(index, 0, customSizeFilterBlock);
                  index++;
                }
                else if (currentProductNumber == priceFilterProductCount) {
                  collectionData.data.data.collection.products.edges.splice(index, 0, customPriceFilterBlock);
                  index++;
                }
                // else if (currentProductNumber == buzzInTownCount) {
                //   collectionData.data.data.collection.products.edges.splice(index, 0, customBuzzInTownBlock);
                //   index++;
                // }
              }
            }
          }
        }
      }

      catch (err) {
        console.log("err:::", err)
      }
      finally {
        return collectionData;
      }


    }
  );

  appmaker.addFilter(
    "shopify-gql-collection-extra-fields",
    "extra-collection-fields",
    (singleCollectionFields) => {
      return {
        ...singleCollectionFields,
        metafields: {
          __args: {
            identifiers: [
              { namespace: "custom", key: "template_name" },
              { namespace: "custom", key: "buzz_in_town_products" }
            ]
          },
          value: true,
          key: true,
          reference: {
            __on: {
              __typeName: "Metaobject",
              fields: {
                key: true,
                value: true
              }
            }
          }
        },
        // Include other collection fields needed for the current functionality
      };
    }
  );

  // addFilter('appmaker-hide-toolbar-icons-on-pages', 'toolbar-icon', () => {
  //   const pages = ['searchPage'];
  //   return pages;
  // });

  addFilter('appmaker-hide-toolbar-icons-on-pages', 'toolbar-icon', () => {
    const pages = ['CheckoutPaymentWebview'];
    return pages;
  });

  appmaker.addFilter(
    'appmaker-shopify-price-data',
    'price-changes',
    (data) => {
      // Set the currency symbol to ₹
      //  data.moneyFormat = data.moneyFormat.replace('Rs. ', '₹'); // to change moneyformat if required
      data.moneyFormat = data.moneyFormat.replace(" ", '');
      data.currency = '₹'; // to change symbol 
      data.amount = data.amount.toLocaleString('en-IN', { minimumFractionDigits: 2, maximumFractionDigits: 2 }); // - you will get the current price , you can alter and sent
      return data;
    }
  );

  addFilter(
    'webview-custom-url-filters',
    'core-custom-schema',
    (currentFilters) => {
      currentFilters = currentFilters.filter(item => item?.action?.action !== "OPEN_MY_ACCOUNT");
      return [...currentFilters];
    },
  );

  // addFilter(
  //   'cart-custom-attributes',
  //   'appmaker-customization',
  //   (defaultAttributes) => {
  //     console.log("defaultAttributes3333", defaultAttributes);
  //     const userData = getUser()
  //     console.log("userData filter", userData);
  //     const isEmployee = userData?.tags?.includes('jockey-employee');
  //     const hasEmployeeAttribute = 'employee' in defaultAttributes;

  //     let updatedAttributes = [...defaultAttributes];

  //     if (isEmployee) {
  //       if (!hasEmployeeAttribute) {
  //         console.log("Adding employee attribute");
  //         updatedAttributes = [
  //           ...updatedAttributes,
  //           { "key": "employee", "value": "true" }
  //         ];
  //       }
  //     } else {
  //       if (hasEmployeeAttribute) {
  //         console.log("Removing employee attribute");
  //         updatedAttributes = updatedAttributes.filter(attr => attr.key !== 'employee');
  //       }
  //     }

  //     console.log("parsedData", updatedAttributes);
  //     return updatedAttributes;
  //   }
  // );


  const STORAGE_VERSION_KEY = '@storage_version';

  const CURRENT_VERSION = '1.0.0';

  async function initializeStorage() {
    try {
      // Retrieve the stored version
      const storedVersion = await AsyncStorage.getItem(STORAGE_VERSION_KEY);

      if (storedVersion !== CURRENT_VERSION) {
        console.log(
          `Storage version mismatch. Current: ${CURRENT_VERSION}, Stored: ${storedVersion}`
        );

        // Handle stale data (clear or migrate)
        await handleVersionMismatch(storedVersion, CURRENT_VERSION);

        // Update the storage version
        await AsyncStorage.setItem(STORAGE_VERSION_KEY, CURRENT_VERSION);

      } else {
        console.log(`Storage version is up-to-date: ${CURRENT_VERSION}`);
      }

    } catch (error) {
      console.error('Error initializing storage:', error);
    }

  }

  async function handleVersionMismatch(storedVersion) {
    if (!storedVersion) {
      console.log('First-time initialization. Setting storage version.');
    } else {
      console.log('Clearing stale storage data...');
      await AsyncStorage.clear();
    }

  }


  initializeStorage();
  registerActions();
  registerToolBarIcons()
  addProductCollection();
  updateFirebaseEvents();
}
const JockeyMobile = {
  id: 'jockey-mobile',
  activate,
  blocks,
  pages
};
registerTheme(JockeyMobile);
export default JockeyMobile;
